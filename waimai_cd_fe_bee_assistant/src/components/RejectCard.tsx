import { openPage } from '@mfe/bee-foundation-utils';
import { TouchableOpacity, Text, View, StyleSheet } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import React, { useState } from 'react';

import { MemoizedMarkdownInner } from './MessageBox/Answer/AnswerContent/MemoizedMarkdownInner';
import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../types';
import { RejectCardMessage } from '../types/message';

interface Props {
    data: RejectCardMessage['insert']['rejectCard'];
    onEndTyping?: () => void;
}

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
    },
    cardItem: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: '#f0f0f0',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    title: {
        fontSize: 16,
        fontWeight: '700',
        color: '#333333',
        marginBottom: 8,
    },
    content: {
        fontSize: 14,
        color: '#666666',
        lineHeight: 20,
        marginBottom: 12,
    },
    descriptionsContainer: {
        marginBottom: 16,
    },
    descriptionItem: {
        flexDirection: 'row',
        marginBottom: 6,
    },
    descriptionLabel: {
        fontSize: 14,
        color: '#999999',
        minWidth: 80,
    },
    descriptionValue: {
        fontSize: 14,
        color: '#333333',
        flex: 1,
    },
    button: {
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'flex-start',
    },
    buttonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    primaryButton: {
        backgroundColor: '#FFD100',
    },
    normalButton: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    primaryButtonText: {
        color: '#333333',
    },
    normalButtonText: {
        color: '#333333',
    },
    expandButton: {
        backgroundColor: '#f5f5f5',
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        marginTop: 8,
    },
    expandButtonText: {
        fontSize: 14,
        color: '#666666',
    },
});

const RejectCard = ({ data, onEndTyping }: Props) => {
    const { content, extendButtonName, showNum } = data;
    const { send } = useSendMessage();
    const [isExpanded, setIsExpanded] = useState(false);

    // 计算显示的内容数量
    const displayContent = React.useMemo(() => {
        if (!showNum || isExpanded) {
            return content;
        }
        return content.slice(0, showNum);
    }, [content, showNum, isExpanded]);

    // 是否显示展开按钮
    const shouldShowExpandButton = React.useMemo(() => {
        return extendButtonName && showNum && content.length > showNum;
    }, [extendButtonName, showNum, content.length]);

    React.useEffect(() => {
        onEndTyping?.();
    }, [onEndTyping]);

    const handleButtonPress = async (button: any) => {
        try {
            // 优先处理action
            if (button.action === 'submitQuestion' && button.question) {
                send(
                    button.question,
                    EntryPointType.USER,
                    EntryPoint.action_card,
                );
                return;
            }

            // 处理URL跳转
            if (button.url) {
                openPage(button.url);
                return;
            }

            // 如果没有action和url，显示提示
            Toast.open('暂无可执行的操作');
        } catch (error) {
            console.error('RejectCard button press error:', error);
            Toast.open('操作失败，请重试');
        }
    };

    const getButtonStyle = (button: any) => {
        const baseStyle = [styles.button];

        // 优先使用自定义颜色
        if (button.color) {
            return [...baseStyle, { backgroundColor: button.color }];
        }

        // 使用type决定样式
        if (button.type === 'primary') {
            return [...baseStyle, styles.primaryButton];
        }

        return [...baseStyle, styles.normalButton];
    };

    const getButtonTextStyle = (button: any) => {
        const baseStyle = [styles.buttonText];

        // 如果有自定义颜色，使用对比色文字
        if (button.color) {
            // 简单的对比度判断
            const isLightColor =
                button.color.toLowerCase().includes('ff') ||
                button.color.toLowerCase().includes('yellow') ||
                button.color.toLowerCase().includes('white');
            return [
                ...baseStyle,
                { color: isLightColor ? '#333333' : '#ffffff' },
            ];
        }

        if (button.type === 'primary') {
            return [...baseStyle, styles.primaryButtonText];
        }

        return [...baseStyle, styles.normalButtonText];
    };

    const handleExpandPress = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <View style={styles.container}>
            {displayContent.map((item, index) => (
                <View key={index} style={styles.cardItem}>
                    <Text style={styles.title} numberOfLines={2}>
                        {item.title}
                    </Text>

                    <View style={styles.content}>
                        <MemoizedMarkdownInner>
                            {item.content}
                        </MemoizedMarkdownInner>
                    </View>

                    {item.descriptions && item.descriptions.length > 0 && (
                        <View style={styles.descriptionsContainer}>
                            {item.descriptions.map((desc, descIndex) => (
                                <View
                                    key={descIndex}
                                    style={styles.descriptionItem}
                                >
                                    <Text style={styles.descriptionLabel}>
                                        {desc.label}:
                                    </Text>
                                    <Text style={styles.descriptionValue}>
                                        {desc.value}
                                    </Text>
                                </View>
                            ))}
                        </View>
                    )}

                    <TouchableOpacity
                        style={getButtonStyle(item.button)}
                        onPress={() => handleButtonPress(item.button)}
                        activeOpacity={0.8}
                    >
                        <Text
                            style={getButtonTextStyle(item.button)}
                            numberOfLines={1}
                        >
                            {item.button.text}
                        </Text>
                    </TouchableOpacity>
                </View>
            ))}

            {shouldShowExpandButton && (
                <TouchableOpacity
                    style={styles.expandButton}
                    onPress={handleExpandPress}
                    activeOpacity={0.8}
                >
                    <Text style={styles.expandButtonText}>
                        {isExpanded ? '收起' : extendButtonName}
                    </Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

export default RejectCard;
