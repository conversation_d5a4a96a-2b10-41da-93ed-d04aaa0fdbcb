import React from 'react';
import { message } from 'antd';
import { RejectCardMessage } from '@src/pages/knowledge/chat/common/type/message';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { EntryPointType, EntryPoint } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import './RejectCard.scss';

interface Props {
    data: RejectCardMessage['insert']['rejectCard'];
    serverId?: string;
    history?: boolean;
}

// 创建高度通信器 - 独立可注入模块
const createHeightCommunicator = (dataJson: string) => {
    return `
        window.data = ${dataJson};
        // 高度通信器 - 独立可注入模块
        (function() {
            const HeightCommunicator = {
                // 通知父窗口高度变化
                notifyHeightChange: function() {
                    const height = document.body.scrollHeight;
                    window.parent.postMessage({
                        type: 'heightChanged',
                        payload: height
                    }, '*');
                },

                // 在DOM变化后延迟通知高度变化
                delayNotifyHeightChange: function(delay = 0) {
                    setTimeout(this.notifyHeightChange.bind(this), delay);
                },

                // 监听DOM变化并自动通知高度变化
                observeHeightChanges: function() {
                    if (typeof ResizeObserver !== 'undefined') {
                        const observer = new ResizeObserver(() => {
                            this.delayNotifyHeightChange(50); // 添加小延迟避免过度频繁
                        });
                        observer.observe(document.body);
                    }

                    // 监听DOM内容变化
                    if (typeof MutationObserver !== 'undefined') {
                        const mutationObserver = new MutationObserver(() => {
                            this.delayNotifyHeightChange(50);
                        });
                        mutationObserver.observe(document.body, {
                            childList: true,
                            subtree: true,
                            attributes: true
                        });
                    }
                },

                // 提供给业务代码调用的接口
                triggerHeightUpdate: function() {
                    this.delayNotifyHeightChange();
                },

                // 初始化高度通信
                init: function(initialDelay = 100) {
                    this.delayNotifyHeightChange(initialDelay);
                    this.observeHeightChanges();
                }
            };

            // 将高度通信器暴露到全局，供业务代码使用
            window.HeightCommunicator = HeightCommunicator;

            // 如果DOM已经加载完成，立即初始化，否则等待
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    HeightCommunicator.init();
                });
            } else {
                HeightCommunicator.init();
            }
        })();
    `;
};

// 注入高度通信器到HTML内容
const injectHeightCommunicator = (htmlContent: string, data: RejectCardMessage['insert']['rejectCard']) => {
    const dataJson = JSON.stringify(data);
    const heightCommunicatorScript = createHeightCommunicator(dataJson);

    // 如果HTML中已经有<script>标签，在第一个<script>前注入
    // 否则在</body>前注入
    if (htmlContent.includes('<script>')) {
        const firstScriptIndex = htmlContent.indexOf('<script>');
        return (
            htmlContent.slice(0, firstScriptIndex) +
            `<script>${heightCommunicatorScript}</script>\n    ` +
            htmlContent.slice(firstScriptIndex)
        );
    } else if (htmlContent.includes('<body>')) {
        return htmlContent.replace('<body>', `<body>\n<script>${heightCommunicatorScript}</script>\n`);
    } else {
        // 如果没有</body>标签，直接在末尾添加
        return `<script>${heightCommunicatorScript}</script>` + htmlContent;
    }
};

// 创建HTML模板字符串 - 移除外部依赖，优化加载速度
const createRejectHtml = (data: RejectCardMessage['insert']['rejectCard']) => {
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reject Card</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        html, body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .container {
            padding: 0;
        }
        .card-container {
            margin-bottom: 16px;
        }
        .card-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
            color: #1f2937;
        }
        .card-item {
            background-color: #f7f7f8;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .card-content {
            font-size: 14px;
            color: #374151;
            margin-bottom: 12px;
            line-height: 1.5;
        }
        .card-content img {
            max-width: 100px;
            border-radius: 8px;
            margin-top: 8px;
        }
        .card-content h1, .card-content h2, .card-content h3, .card-content h4, .card-content h5, .card-content h6 {
            margin: 12px 0 8px 0;
            font-weight: 600;
        }
        .card-content p {
            margin: 8px 0;
        }
        .card-content ul, .card-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }
        .card-content li {
            margin: 4px 0;
        }
        .card-content code {
            background-color: #f1f5f9;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .card-content pre {
            background-color: #f1f5f9;
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 8px 0;
        }
        .card-content pre code {
            background-color: transparent;
            padding: 0;
        }
        .card-content strong {
            font-weight: 600;
        }
        .card-content em {
            font-style: italic;
        }
        .card-content a {
            color: #3b82f6;
            text-decoration: none;
        }
        .card-content a:hover {
            text-decoration: underline;
        }
        .card-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 12px;
            margin: 8px 0;
            color: #6b7280;
        }
        .descriptions-container {
            margin-bottom: 16px;
        }
        .description-item {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        .btn-primary {
            background-color: #FFD100;
            color: #333333;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            width: 100%;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .btn-primary:hover {
            background-color: #f2c300;
        }
        .btn-normal {
            background-color: #FFFFFF;
            border: 1px solid #E5E5E5;
            color: #333333;
            border-radius: 8px;
            padding: 10px 16px;
            width: 100%;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .btn-normal:hover {
            background-color: #f9fafb;
        }
        .extend-button {
            color: #999999;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            padding: 8px;
        }
        .extend-button:hover {
            color: #666666;
        }
        .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-left: 4px;
            vertical-align: middle;
        }
        .text-center {
            text-align: center;
        }
        .space-y-3 > *:not(:first-child) {
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="rejectCardContainer" class="space-y-3"></div>
        <div id="extendButtonContainer" class="text-center"></div>
    </div>

    <script>
        // 业务数据
        document.addEventListener('DOMContentLoaded', function () {
            const data = window.data;
            const container = document.getElementById('rejectCardContainer');
            const extendContainer = document.getElementById('extendButtonContainer');

            if (!data || !container) {
                console.error('Card data or container not found.');
                return;
            }

            const allCards = data.content;
            const showNum = data.showNum;
            let isExtended = false;

            const renderCards = () => {
                container.innerHTML = '';
                const cardsToShow = isExtended || !showNum || showNum >= allCards.length
                    ? allCards
                    : allCards.slice(0, showNum);

                cardsToShow.forEach(item => {
                    // 创建卡片容器
                    const cardContainer = document.createElement('div');
                    cardContainer.className = 'card-container';

                    // 创建标题元素（放在卡片外面）
                    const titleElement = document.createElement('h2');
                    titleElement.className = 'card-title';
                    titleElement.textContent = item.title;

                    // 创建卡片元素
                    const card = document.createElement('div');
                    card.className = 'card-item';

                    const descriptionsHtml = item.descriptions.map(desc => \`
                        <p class="description-item">\${desc.label}: \${desc.value}</p>
                    \`).join('');

                    const buttonClass = item.button.type === 'primary' ? 'btn-primary' : 'btn-normal';

                    // 使用marked.js解析markdown内容
                    const parsedContent = typeof marked !== 'undefined' ? marked.parse(item.content || '') : (item.content || '');

                    // 卡片内容不包含标题（内容已预处理）
                    card.innerHTML = \`
                        <div class="card-content">\${parsedContent}</div>
                        <div class="descriptions-container">\${descriptionsHtml}</div>
                        <button class="\${buttonClass}">
                            \${item.button.text}
                        </button>
                    \`;

                    const buttonEl = card.querySelector('button');
                    buttonEl.addEventListener('click', () => {
                        if (item.button.action === 'submitQuestion') {
                            console.log('Action: submitQuestion, Question:', item.button.question);
                            window.parent.postMessage({
                                type: 'submitQuestion',
                                payload: item.button.question
                            }, '*');
                        } else if (item.button.url) {
                            console.log('Action: navigate, URL:', item.button.url);
                            window.parent.postMessage({
                                type: 'navigate',
                                payload: item.button.url
                            }, '*');
                        } else {
                            console.log('Button clicked:', item.button.text);
                        }
                    });

                    // 将标题和卡片添加到容器
                    cardContainer.appendChild(titleElement);
                    cardContainer.appendChild(card);
                    container.appendChild(cardContainer);
                });

                // 渲染完成后通知高度变化（使用高度通信器）
                if (window.HeightCommunicator) {
                    window.HeightCommunicator.triggerHeightUpdate();
                }
            };

            const renderExtendButton = () => {
                extendContainer.innerHTML = '';
                if (data.extendButtonName && allCards.length > showNum) {
                    const buttonText = isExtended ? '收起' : \`\${data.extendButtonName}\`;
                    const icon = isExtended
                        ? \`<svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" /></svg>\`
                        : \`<svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>\`;

                    const extendButton = document.createElement('button');
                    extendButton.className = 'extend-button';
                    extendButton.innerHTML = \`\${buttonText} \${icon}\`;

                    extendButton.addEventListener('click', () => {
                        isExtended = !isExtended;
                        renderCards();
                        renderExtendButton();
                    });
                    extendContainer.appendChild(extendButton);
                }

                // 渲染完成后通知高度变化（使用高度通信器）
                if (window.HeightCommunicator) {
                    window.HeightCommunicator.triggerHeightUpdate();
                }
            };

            renderCards();
            renderExtendButton();
        });
    </script>
</body>
</html>
    `;

    // 注入高度通信器
    const finalHtml = injectHeightCommunicator(htmlContent, data);
    return finalHtml;
};

const RejectCard: React.FC<Props> = ({ data, serverId, history }) => {
    const sendMessage = useSendMessage();
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();
    const [iframeHeight, setIframeHeight] = React.useState(200); // 初始高度
    const [isLoading, setIsLoading] = React.useState(true); // 添加loading状态

    // 处理来自 iframe 的消息
    React.useEffect(() => {
        const handleMessage = async (event: MessageEvent) => {
            if (event.data.type === 'submitQuestion') {
                try {
                    await sendMessage(event.data.payload, {
                        entryPointType: EntryPointType.USER,
                        entryPoint: EntryPoint.reject_card,
                    });
                } catch (error) {
                    console.error('Send message error:', error);
                    message.error('发送消息失败，请重试');
                }
            } else if (event.data.type === 'navigate') {
                try {
                    openLink(event.data.payload, serverId, sessionId, bizInfo?.uid, history);
                } catch (error) {
                    console.error('Navigate error:', error);
                    message.error('跳转失败，请重试');
                }
            } else if (event.data.type === 'heightChanged') {
                // 更新iframe高度，添加一些额外的边距
                const newHeight = Math.max(event.data.payload + 20, 100); // 最小高度100px
                setIframeHeight(newHeight);
                // 高度变化表示内容已加载完成
                setIsLoading(false);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [sendMessage, serverId, sessionId, bizInfo?.uid, history]);

    const htmlContent = React.useMemo(() => createRejectHtml(data), [data]);

    return (
        <div style={{ position: 'relative' }}>
            {isLoading && (
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f7f7f8',
                        borderRadius: '8px',
                        zIndex: 1,
                    }}
                >
                    <div style={{ color: '#666', fontSize: '14px' }}>加载中...</div>
                </div>
            )}
            <iframe
                srcDoc={htmlContent}
                style={{
                    width: '100%',
                    height: `${iframeHeight}px`,
                    border: 'none',
                    borderRadius: '8px',
                    transition: 'height 0.3s ease', // 添加高度变化动画
                    overflow: 'hidden', // 隐藏iframe滚动条
                    opacity: isLoading ? 0.5 : 1,
                }}
                sandbox="allow-scripts allow-same-origin"
            />
        </div>
    );
};

// 导出高度通信器注入函数，供其他组件使用
export const injectHeightCommunicatorToHtml = injectHeightCommunicator;

export default RejectCard;
